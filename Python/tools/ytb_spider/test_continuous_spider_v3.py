#!/usr/bin/env python3
"""
YouTube持续蜘蛛式爬虫测试版 v3.0
快速测试版本，用于验证功能

使用方法:
1. 在Google Colab中运行
2. 自动使用webshare代理池
3. 持续爬取并显示Twitter链接

作者: GreenJoson
创建时间: 2025-06-26
版本: v3.0 - 测试版
"""

import sys
import os

# 添加路径以便导入主模块
sys.path.append('/Users/<USER>/Projects/Python/tools/ytb_spider')

try:
    from youtube_continuous_spider_v3 import ContinuousSpider
    print("✅ 成功导入持续爬虫模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 youtube_continuous_spider_v3.py 文件存在")
    sys.exit(1)

def test_proxy_pool():
    """测试代理池"""
    print("\n🧪 测试代理池...")
    
    spider = ContinuousSpider()
    proxy_stats = spider.proxy_pool.get_stats()
    
    print(f"📊 代理池状态:")
    print(f"   总代理数: {proxy_stats['total_proxies']}")
    print(f"   可用代理: {proxy_stats['active_proxies']}")
    print(f"   成功率: {proxy_stats['success_rate']}")
    
    # 测试获取代理
    proxy = spider.proxy_pool.get_proxy()
    if proxy:
        print(f"✅ 成功获取代理: {proxy['username']}@{proxy['host']}")
    else:
        print("❌ 无法获取代理")
    
    return spider

def test_twitter_extraction():
    """测试Twitter链接提取"""
    print("\n🧪 测试Twitter链接提取...")
    
    spider = ContinuousSpider()
    
    # 测试HTML内容
    test_html = '''
    <html>
    <body>
        <a href="https://twitter.com/MrBeast">Twitter</a>
        <a href="https://x.com/PewDiePie">X.com</a>
        <span>Follow us on twitter.com/TestChannel</span>
    </body>
    </html>
    '''
    
    twitter_links = spider.twitter_extractor.extract_from_html(test_html)
    
    print(f"🔍 发现的Twitter链接:")
    for link in twitter_links:
        print(f"   🔗 {link}")
    
    return len(twitter_links) > 0

def run_quick_test():
    """运行快速测试"""
    print("🚀 YouTube持续蜘蛛式爬虫 v3.0 - 快速测试")
    print("=" * 60)
    
    # 测试代理池
    spider = test_proxy_pool()
    
    # 测试Twitter提取
    twitter_test_passed = test_twitter_extraction()
    
    if not twitter_test_passed:
        print("❌ Twitter提取测试失败")
        return
    
    print("\n🕷️ 开始小规模爬取测试...")
    
    # 添加种子频道
    seed_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie"
    ]
    
    spider.add_seed_channels(seed_channels)
    
    print(f"🌱 种子频道: {len(seed_channels)} 个")
    
    try:
        # 小规模测试爬取
        spider.continuous_crawl(
            max_total_crawl=10,  # 只爬取10个频道进行测试
            max_discover_per_channel=5  # 每个频道发现5个新频道
        )
        
        print("\n✅ 测试完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        spider.stop()
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
    
    return spider

def run_full_continuous_crawl():
    """运行完整的持续爬取"""
    print("🚀 YouTube持续蜘蛛式爬虫 v3.0 - 完整版")
    print("=" * 60)
    
    spider = ContinuousSpider()
    
    # 添加更多种子频道
    seed_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie", 
        "https://www.youtube.com/@Markiplier",
        "https://www.youtube.com/@jacksepticeye",
        "https://www.youtube.com/@DudePerfect"
    ]
    
    spider.add_seed_channels(seed_channels)
    
    print(f"🌱 种子频道: {len(seed_channels)} 个")
    print("🚀 开始持续爬取...")
    print("💡 按 Ctrl+C 可以随时停止")
    
    try:
        # 持续爬取
        spider.continuous_crawl(
            max_total_crawl=200,  # 爬取200个频道
            max_discover_per_channel=8  # 每个频道发现8个新频道
        )
        
        print("\n🏁 爬取完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断爬取")
        spider.stop()
    except Exception as e:
        print(f"\n❌ 爬取出错: {e}")
    
    return spider

def show_twitter_summary(spider):
    """显示Twitter发现总结"""
    if not spider.twitter_discoveries:
        print("\n🐦 未发现任何Twitter链接")
        return
    
    print(f"\n🎉 Twitter发现总结:")
    print("=" * 60)
    
    all_twitter_links = set()
    for discovery in spider.twitter_discoveries:
        for link in discovery['twitter_links']:
            all_twitter_links.add(link)
    
    print(f"📊 统计:")
    print(f"   🏆 发现频道数: {len(spider.twitter_discoveries)}")
    print(f"   🔗 Twitter链接总数: {len(all_twitter_links)}")
    print(f"   📈 发现率: {(len(spider.twitter_discoveries)/max(spider.total_crawled, 1))*100:.1f}%")
    
    print(f"\n🔗 所有发现的Twitter链接:")
    for i, link in enumerate(sorted(all_twitter_links), 1):
        print(f"   {i:2d}. {link}")

def main():
    """主函数"""
    print("选择运行模式:")
    print("1. 快速测试 (10个频道)")
    print("2. 完整爬取 (200个频道)")
    print("3. 仅测试代理池")
    
    try:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            spider = run_quick_test()
        elif choice == "2":
            spider = run_full_continuous_crawl()
        elif choice == "3":
            spider = test_proxy_pool()
            return
        else:
            print("❌ 无效选择")
            return
        
        # 显示最终总结
        show_twitter_summary(spider)
        
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
