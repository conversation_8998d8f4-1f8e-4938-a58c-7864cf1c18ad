#!/usr/bin/env python3
"""
YouTube Twitter爬虫 - 最终统一版本
集成所有功能的终极版本，支持Google Colab和本地运行

功能特点:
🕷️ 蜘蛛式频道发现 - 自动发现相关频道
🔄 持续爬取模式 - 不会停止，一直爬取
🌐 智能IP切换 - 自动切换代理，错误恢复
🐦 实时Twitter展示 - 立即显示发现的Twitter链接
📊 详细统计信息 - 实时监控爬取状态

作者: GreenJoson
版本: Final v1.0 - 最终统一版本
创建时间: 2025-06-26
"""

import requests
import json
import time
import random
import re
import os
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProxyManager:
    """代理管理器 - 支持Webshare代理池"""

    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = self._load_proxies()
        self.failed_proxies = set()
        self.current_index = 0
        self.consecutive_failures = 0

        if self.proxies:
            print(f"✅ 代理池加载成功: {len(self.proxies)} 个代理")
        else:
            print("⚠️ 未加载到代理，将使用直连模式")

    def _load_proxies(self) -> List[Dict]:
        """加载代理列表"""
        proxies = []

        if not os.path.exists(self.proxy_file):
            print(f"📁 代理文件不存在: {self.proxy_file}")
            return []

        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            # 解析格式: p.webshare.io:80:username:password
                            parts = line.split(':')
                            if len(parts) >= 4:
                                host, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{host}:{port}"
                                proxies.append({
                                    'http': proxy_url,
                                    'https': proxy_url,
                                    'id': line_num,
                                    'username': username,
                                    'host': host
                                })
                        except Exception as e:
                            logger.debug(f"解析代理失败 (行{line_num}): {e}")
        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")

        return proxies

    def get_proxy(self) -> Optional[Dict]:
        """获取可用代理"""
        if not self.proxies:
            return None

        available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]

        if not available_proxies:
            print("🔄 重置失败代理列表")
            self.failed_proxies.clear()
            self.consecutive_failures = 0
            available_proxies = self.proxies

        if available_proxies:
            # 连续失败时跳过一些代理
            if self.consecutive_failures >= 3:
                skip_count = random.randint(5, 15)
                self.current_index += skip_count
                self.consecutive_failures = 0
                print(f"🔀 连续失败，跳过 {skip_count} 个代理")

            proxy = available_proxies[self.current_index % len(available_proxies)]
            self.current_index += 1
            return proxy

        return None

    def mark_failed(self, proxy_id: int):
        """标记代理失败"""
        if proxy_id:
            self.failed_proxies.add(proxy_id)
            self.consecutive_failures += 1

    def mark_success(self, proxy_id: int):
        """标记代理成功"""
        self.consecutive_failures = 0

    def get_stats(self) -> Dict:
        """获取代理统计"""
        if not self.proxies:
            return {'total': 0, 'active': 0, 'failed': 0, 'success_rate': '0%'}

        total = len(self.proxies)
        failed = len(self.failed_proxies)
        active = total - failed

        return {
            'total': total,
            'active': active,
            'failed': failed,
            'success_rate': f"{(active/total)*100:.1f}%" if total > 0 else "0%"
        }

class TwitterExtractor:
    """Twitter链接提取器"""

    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]

    def extract(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()

        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 标准化链接
                if not match.startswith('http'):
                    match = f"https://{match}"

                # 清理链接
                clean_link = match.split('?')[0].split('#')[0]

                # 转换x.com为twitter.com
                clean_link = clean_link.replace('x.com', 'twitter.com')

                twitter_links.add(clean_link)

        return sorted(list(twitter_links))

class YouTubeSpider:
    """YouTube蜘蛛爬虫 - 最终统一版本"""

    def __init__(self):
        self.proxy_manager = ProxyManager()
        self.twitter_extractor = TwitterExtractor()
        self.session = requests.Session()

        # 爬取状态
        self.discovered_channels = set()
        self.crawled_channels = set()
        self.crawl_queue = []
        self.twitter_discoveries = []
        self.total_crawled = 0
        self.is_running = True

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })

        print("🕷️ YouTube蜘蛛爬虫初始化完成")

    def add_seeds(self, channels: List[str]):
        """添加种子频道"""
        for channel in channels:
            if channel not in self.discovered_channels:
                self.crawl_queue.append(channel)
                self.discovered_channels.add(channel)

        print(f"🌱 添加种子频道: {len(channels)} 个")

    def crawl_channel(self, channel_url: str) -> Dict:
        """爬取单个频道"""
        result = {
            'channel_url': channel_url,
            'twitter_links': [],
            'success': False,
            'error': None,
            'proxy_used': '直连'
        }

        try:
            proxy = self.proxy_manager.get_proxy()
            if proxy:
                result['proxy_used'] = f"{proxy['username']}@{proxy['host']}"

            # 访问频道about页面
            about_url = f"{channel_url.rstrip('/')}/about"

            response = self.session.get(
                about_url,
                proxies=proxy,
                timeout=25,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_manager.mark_success(proxy['id'])

                # 提取Twitter链接
                twitter_links = self.twitter_extractor.extract(response.text)
                result['twitter_links'] = twitter_links
                result['success'] = True

                if twitter_links:
                    print(f"✅ 发现Twitter: {channel_url}")
                    for link in twitter_links:
                        print(f"   🔗 {link}")

                    self.twitter_discoveries.append({
                        'channel_url': channel_url,
                        'twitter_links': twitter_links,
                        'discovered_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'proxy_used': result['proxy_used']
                    })
                else:
                    print(f"⚪ 无Twitter: {channel_url}")

            else:
                if proxy:
                    self.proxy_manager.mark_failed(proxy['id'])
                result['error'] = f"HTTP {response.status_code}"
                print(f"❌ 访问失败: {channel_url} ({response.status_code})")

        except Exception as e:
            if proxy:
                self.proxy_manager.mark_failed(proxy['id'])
            result['error'] = str(e)
            print(f"❌ 爬取错误: {channel_url} - {e}")

        return result

    def discover_channels(self, channel_url: str, max_discover: int = 6) -> List[str]:
        """从频道发现推荐频道"""
        try:
            base_url = channel_url.rstrip('/')
            discovered = set()

            # 从channels页面发现
            channels_page = f"{base_url}/channels"
            channels_from_page = self._extract_channels_from_url(channels_page)
            discovered.update(channels_from_page)

            # 从主页面发现
            if len(discovered) < max_discover:
                channels_from_main = self._extract_channels_from_url(base_url)
                discovered.update(channels_from_main)

            # 过滤新频道
            new_channels = []
            for url in discovered:
                if (len(new_channels) >= max_discover):
                    break
                if (url not in self.discovered_channels and
                    url != channel_url and
                    self._is_valid_channel_url(url)):
                    new_channels.append(url)
                    self.discovered_channels.add(url)

            return new_channels

        except Exception as e:
            logger.debug(f"发现频道失败: {e}")
            return []

    def _extract_channels_from_url(self, url: str) -> List[str]:
        """从URL提取频道链接"""
        try:
            proxy = self.proxy_manager.get_proxy()

            response = self.session.get(
                url,
                proxies=proxy,
                timeout=20,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_manager.mark_success(proxy['id'])
                return self._extract_channel_urls_from_html(response.text)
            else:
                if proxy:
                    self.proxy_manager.mark_failed(proxy['id'])

        except Exception as e:
            if proxy:
                self.proxy_manager.mark_failed(proxy['id'])
            logger.debug(f"提取频道链接失败: {e}")

        return []

    def _extract_channel_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取频道URL"""
        channel_patterns = [
            r'https://www\.youtube\.com/@[a-zA-Z0-9_.-]+',
            r'https://www\.youtube\.com/c/[a-zA-Z0-9_.-]+',
            r'https://www\.youtube\.com/channel/[a-zA-Z0-9_-]+',
            r'https://www\.youtube\.com/user/[a-zA-Z0-9_.-]+',
            r'/(@[a-zA-Z0-9_.-]+)',
            r'/(c/[a-zA-Z0-9_.-]+)',
            r'/(channel/[a-zA-Z0-9_-]+)',
            r'/(user/[a-zA-Z0-9_.-]+)'
        ]

        channels = set()

        for pattern in channel_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/'):
                    match = f"https://www.youtube.com{match}"
                elif not match.startswith('http'):
                    match = f"https://www.youtube.com/{match}"

                if self._is_valid_channel_url(match):
                    channels.add(match)

        return list(channels)

    def _is_valid_channel_url(self, url: str) -> bool:
        """验证YouTube频道URL"""
        if not url or not isinstance(url, str):
            return False

        valid_patterns = [
            r'https://www\.youtube\.com/@[a-zA-Z0-9_.-]+$',
            r'https://www\.youtube\.com/c/[a-zA-Z0-9_.-]+$',
            r'https://www\.youtube\.com/channel/[a-zA-Z0-9_-]+$',
            r'https://www\.youtube\.com/user/[a-zA-Z0-9_.-]+$'
        ]

        return any(re.match(pattern, url) for pattern in valid_patterns)

    def run_continuous(self, max_total: int = 200, max_discover_per_channel: int = 8):
        """持续爬取模式"""
        print(f"🚀 开始持续爬取 (目标: {max_total} 个频道)")
        print("💡 按 Ctrl+C 可随时停止")

        try:
            while self.is_running and self.total_crawled < max_total:
                if not self.crawl_queue:
                    print("📋 队列为空，爬取完成")
                    break

                # 获取下一个频道
                channel_url = self.crawl_queue.pop(0)
                self.crawled_channels.add(channel_url)

                print(f"\n🔍 爬取频道 [{self.total_crawled + 1}/{max_total}]: {channel_url}")

                # 爬取频道
                result = self.crawl_channel(channel_url)
                self.total_crawled += 1

                # 发现新频道
                if result['success']:
                    new_channels = self.discover_channels(channel_url, max_discover_per_channel)
                    if new_channels:
                        self.crawl_queue.extend(new_channels)
                        print(f"🕷️ 发现新频道: {len(new_channels)} 个")

                # 显示统计
                if self.total_crawled % 10 == 0:
                    self.show_stats()

                # 延迟
                time.sleep(random.uniform(2, 4))

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断爬取")
            self.is_running = False
        except Exception as e:
            print(f"\n❌ 爬取出错: {e}")

        print("\n🏁 爬取结束")
        self.show_final_summary()

    def show_stats(self):
        """显示统计信息"""
        proxy_stats = self.proxy_manager.get_stats()

        print(f"\n📊 爬取统计:")
        print(f"   🕷️ 已发现频道: {len(self.discovered_channels)}")
        print(f"   ✅ 已爬取频道: {self.total_crawled}")
        print(f"   📋 队列中频道: {len(self.crawl_queue)}")
        print(f"   🐦 发现Twitter: {len(self.twitter_discoveries)} 个")
        if self.total_crawled > 0:
            print(f"   📈 Twitter发现率: {(len(self.twitter_discoveries)/self.total_crawled)*100:.1f}%")
        print(f"   🌐 代理状态: {proxy_stats['active']}/{proxy_stats['total']} 可用 ({proxy_stats['success_rate']})")

    def show_final_summary(self):
        """显示最终总结"""
        print(f"\n🎉 爬取总结:")
        print("=" * 60)

        self.show_stats()

        if self.twitter_discoveries:
            print(f"\n🐦 发现的Twitter链接 (共 {len(self.twitter_discoveries)} 个):")
            print("-" * 60)

            all_twitter_links = set()
            for i, discovery in enumerate(self.twitter_discoveries, 1):
                print(f"\n{i}. YouTube频道: {discovery['channel_url']}")
                print(f"   发现时间: {discovery['discovered_at']}")
                print(f"   使用代理: {discovery['proxy_used']}")
                print("   Twitter链接:")
                for link in discovery['twitter_links']:
                    print(f"   🔗 {link}")
                    all_twitter_links.add(link)
                print("   " + "-" * 58)

            print(f"\n📋 所有Twitter链接汇总 (去重后 {len(all_twitter_links)} 个):")
            for i, link in enumerate(sorted(all_twitter_links), 1):
                print(f"   {i:2d}. {link}")
        else:
            print("\n🐦 未发现任何Twitter链接")

# 快速启动函数
def quick_start():
    """快速启动函数"""
    print("🕷️ YouTube Twitter爬虫 - 最终统一版本")
    print("=" * 50)

    spider = YouTubeSpider()

    # 默认种子频道
    seed_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie",
        "https://www.youtube.com/@Markiplier"
    ]

    spider.add_seeds(seed_channels)

    print("\n选择运行模式:")
    print("1. 快速测试 (20个频道)")
    print("2. 中等规模 (100个频道)")
    print("3. 大规模爬取 (200个频道)")
    print("4. 自定义数量")

    try:
        choice = input("\n请选择 (1-4): ").strip()

        if choice == "1":
            spider.run_continuous(max_total=20, max_discover_per_channel=5)
        elif choice == "2":
            spider.run_continuous(max_total=100, max_discover_per_channel=8)
        elif choice == "3":
            spider.run_continuous(max_total=200, max_discover_per_channel=10)
        elif choice == "4":
            max_total = int(input("请输入爬取数量: "))
            spider.run_continuous(max_total=max_total)
        else:
            print("❌ 无效选择，使用默认模式")
            spider.run_continuous(max_total=50)

    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    quick_start()
