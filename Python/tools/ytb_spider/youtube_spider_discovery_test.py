#!/usr/bin/env python3
"""
YouTube频道推荐发现功能测试版本
专门测试从种子频道发现推荐频道的功能

作者: GreenJoson
创建时间: 2025-06-26
版本: v1.0 - 推荐频道发现功能
"""

import requests
import re
import time
import random
from typing import List, Set
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleChannelDiscovery:
    """简单的频道发现器"""
    
    def __init__(self, use_proxy: bool = False):
        self.session = requests.Session()
        self.discovered_channels = set()
        self.use_proxy = use_proxy
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
        logger.info("频道发现器初始化完成")
    
    def discover_from_channel(self, seed_url: str, max_discover: int = 10) -> List[str]:
        """
        从种子频道发现推荐频道
        
        Args:
            seed_url: 种子频道URL
            max_discover: 最大发现数量
            
        Returns:
            List[str]: 发现的频道URL列表
        """
        logger.info(f"🔍 开始从种子频道发现: {seed_url}")
        
        try:
            # 清理URL
            base_url = seed_url.rstrip('/')
            if '/videos' in base_url or '/about' in base_url:
                base_url = base_url.split('/videos')[0].split('/about')[0]
            
            discovered = set()
            
            # 方法1: 尝试访问channels页面
            channels_page = f"{base_url}/channels"
            channels_from_page = self._get_channels_from_url(channels_page)
            discovered.update(channels_from_page)
            logger.info(f"  从channels页面发现: {len(channels_from_page)} 个")
            
            # 方法2: 从主页面发现
            if len(discovered) < max_discover:
                channels_from_main = self._get_channels_from_url(base_url)
                discovered.update(channels_from_main)
                logger.info(f"  从主页面发现: {len(channels_from_main)} 个")
            
            # 过滤和去重
            result = []
            for url in discovered:
                if len(result) >= max_discover:
                    break
                if url not in self.discovered_channels and url != seed_url:
                    result.append(url)
                    self.discovered_channels.add(url)
            
            logger.info(f"✅ 总共发现 {len(result)} 个新频道")
            return result
            
        except Exception as e:
            logger.error(f"发现频道失败: {e}")
            return []
    
    def _get_channels_from_url(self, url: str) -> List[str]:
        """从指定URL获取频道链接"""
        try:
            response = self.session.get(url, timeout=20)
            
            if response.status_code == 200:
                return self._extract_channel_urls(response.text)
            else:
                logger.debug(f"页面访问失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.debug(f"访问页面异常: {e}")
            return []
    
    def _extract_channel_urls(self, html: str) -> List[str]:
        """从HTML中提取频道URL"""
        channels = set()
        
        # YouTube频道URL模式
        patterns = [
            r'https://www\.youtube\.com/@([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/c/([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/channel/([a-zA-Z0-9_.-]+)',
            r'"/@([a-zA-Z0-9_.-]+)"',
            r'"/c/([a-zA-Z0-9_.-]+)"',
            r'"/channel/([a-zA-Z0-9_.-]+)"',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                if match.startswith('https://'):
                    channels.add(match)
                else:
                    # 构建完整URL
                    if '/' in match:
                        full_url = f"https://www.youtube.com/{match}"
                    else:
                        full_url = f"https://www.youtube.com/@{match}"
                    channels.add(full_url)
        
        # 验证URL
        valid_channels = []
        for url in channels:
            if self._is_valid_channel_url(url):
                valid_channels.append(url)
        
        return valid_channels[:15]  # 限制数量
    
    def _is_valid_channel_url(self, url: str) -> bool:
        """验证频道URL"""
        if not url or not url.startswith('https://www.youtube.com/'):
            return False
        
        # 检查有效模式
        patterns = [
            r'youtube\.com/@[a-zA-Z0-9_.-]+$',
            r'youtube\.com/c/[a-zA-Z0-9_.-]+$',
            r'youtube\.com/channel/[a-zA-Z0-9_.-]+$',
        ]
        
        return any(re.search(p, url, re.IGNORECASE) for p in patterns)

def test_channel_discovery():
    """测试频道发现功能"""
    print("🕷️ YouTube频道推荐发现功能测试")
    print("=" * 50)
    
    # 测试种子频道
    seed_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@MKBHD", 
        "https://www.youtube.com/@UnboxTherapy"
    ]
    
    discoverer = SimpleChannelDiscovery()
    
    all_discovered = []
    
    for i, seed in enumerate(seed_channels, 1):
        print(f"\n🌱 种子频道 {i}: {seed}")
        
        discovered = discoverer.discover_from_channel(seed, max_discover=8)
        
        if discovered:
            print(f"  🔍 发现的推荐频道:")
            for j, channel in enumerate(discovered, 1):
                print(f"    {j}. {channel}")
            all_discovered.extend(discovered)
        else:
            print(f"  ⚠️  未发现推荐频道")
        
        # 请求间隔
        if i < len(seed_channels):
            delay = random.uniform(3, 6)
            print(f"  ⏳ 等待 {delay:.1f} 秒...")
            time.sleep(delay)
    
    print(f"\n📊 测试结果统计:")
    print(f"  🌱 种子频道数: {len(seed_channels)}")
    print(f"  🔍 发现频道数: {len(all_discovered)}")
    print(f"  📈 平均发现率: {len(all_discovered)/len(seed_channels):.1f} 个/种子")
    
    print(f"\n🎯 所有发现的频道:")
    for i, channel in enumerate(set(all_discovered), 1):
        print(f"  {i}. {channel}")
    
    return all_discovered

if __name__ == '__main__':
    try:
        discovered_channels = test_channel_discovery()
        print(f"\n🎉 测试完成！发现了 {len(set(discovered_channels))} 个独特频道")
        
        print(f"\n💡 下一步可以:")
        print(f"1. 将发现的频道作为新的种子继续发现")
        print(f"2. 爬取这些频道的详细信息和Twitter链接")
        print(f"3. 构建完整的蜘蛛式爬取系统")
        
    except KeyboardInterrupt:
        print(f"\n👋 用户取消测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
