#!/usr/bin/env python3
"""
YouTube频道持续蜘蛛式爬虫 v3.0
支持持续爬取、自动IP切换、Twitter链接展示

功能特点:
- 🕷️ 蜘蛛式频道发现和持续爬取
- 🔄 自动IP切换和错误恢复
- 🐦 详细的Twitter链接展示
- 📊 实时统计和进度显示
- 🛡️ 强大的错误处理和重试机制

作者: GreenJoson
创建时间: 2025-06-26
版本: v3.0 - 持续爬取增强版
"""

import requests
import sqlite3
import json
import time
import random
import re
import threading
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('youtube_continuous_spider.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """代理配置类"""
    proxy_file: str = "webshare_proxy.txt"  # Google Colab中使用相对路径
    max_failures: int = 5
    timeout: int = 25
    switch_threshold: int = 3  # 连续失败3次后切换IP

class WebshareProxyPool:
    """Webshare代理池管理器"""

    def __init__(self, config: ProxyConfig):
        self.config = config
        self.proxies = self._load_proxies()
        self.failed_proxies = set()
        self.current_index = 0
        self.proxy_stats = {}
        self.lock = threading.Lock()
        self.consecutive_failures = 0

        logger.info(f"代理池初始化完成，共 {len(self.proxies)} 个代理")

    def _load_proxies(self) -> List[Dict]:
        """从文件加载代理列表"""
        proxies = []
        try:
            with open(self.config.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            # 解析格式: p.webshare.io:80:username:password
                            parts = line.split(':')
                            if len(parts) >= 4:
                                host, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{host}:{port}"
                                proxies.append({
                                    'http': proxy_url,
                                    'https': proxy_url,
                                    'id': line_num,
                                    'username': username,
                                    'host': host
                                })
                        except Exception as e:
                            logger.warning(f"解析代理失败 (行{line_num}): {e}")

        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")

        return proxies

    def get_proxy(self) -> Optional[Dict]:
        """获取可用代理"""
        with self.lock:
            available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]

            if not available_proxies:
                logger.warning("所有代理都已失败，重置失败列表")
                self.failed_proxies.clear()
                self.consecutive_failures = 0
                available_proxies = self.proxies

            if available_proxies:
                # 如果连续失败次数过多，跳过一些代理
                if self.consecutive_failures >= self.config.switch_threshold:
                    self.current_index += random.randint(5, 15)  # 跳过5-15个代理
                    self.consecutive_failures = 0
                    logger.info(f"连续失败过多，跳转到代理 {self.current_index}")

                proxy = available_proxies[self.current_index % len(available_proxies)]
                self.current_index += 1
                return proxy

            return None

    def mark_proxy_failed(self, proxy_id: int):
        """标记代理失败"""
        with self.lock:
            self.failed_proxies.add(proxy_id)
            self.consecutive_failures += 1

            if proxy_id not in self.proxy_stats:
                self.proxy_stats[proxy_id] = {'failures': 0, 'successes': 0}
            self.proxy_stats[proxy_id]['failures'] += 1

            logger.warning(f"代理 {proxy_id} 失败 (连续失败: {self.consecutive_failures})")

    def mark_proxy_success(self, proxy_id: int):
        """标记代理成功"""
        with self.lock:
            self.consecutive_failures = 0  # 重置连续失败计数

            if proxy_id not in self.proxy_stats:
                self.proxy_stats[proxy_id] = {'failures': 0, 'successes': 0}
            self.proxy_stats[proxy_id]['successes'] += 1

    def get_stats(self) -> Dict:
        """获取代理统计"""
        with self.lock:
            total_proxies = len(self.proxies)
            failed_proxies = len(self.failed_proxies)
            active_proxies = total_proxies - failed_proxies

            return {
                'total_proxies': total_proxies,
                'active_proxies': active_proxies,
                'failed_proxies': failed_proxies,
                'consecutive_failures': self.consecutive_failures,
                'success_rate': f"{(active_proxies/total_proxies)*100:.1f}%" if total_proxies > 0 else "0%"
            }

class TwitterExtractor:
    """Twitter链接提取器"""

    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]

    def extract_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()

        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 标准化链接
                if not match.startswith('http'):
                    match = f"https://{match}"

                # 清理链接
                clean_link = match.split('?')[0].split('#')[0]

                # 转换x.com为twitter.com
                clean_link = clean_link.replace('x.com', 'twitter.com')

                twitter_links.add(clean_link)

        return sorted(list(twitter_links))

class ContinuousSpider:
    """持续蜘蛛式爬虫"""

    def __init__(self, proxy_file: str = None):
        # 初始化组件
        proxy_config = ProxyConfig()
        if proxy_file:
            proxy_config.proxy_file = proxy_file

        self.proxy_pool = WebshareProxyPool(proxy_config)
        self.twitter_extractor = TwitterExtractor()
        self.session = requests.Session()

        # 爬取状态
        self.discovered_channels = set()
        self.crawled_channels = set()
        self.crawl_queue = []
        self.twitter_discoveries = []
        self.total_crawled = 0
        self.is_running = True

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })

        logger.info("持续蜘蛛式爬虫初始化完成")

    def add_seed_channels(self, seed_channels: List[str]):
        """添加种子频道"""
        for channel in seed_channels:
            if channel not in self.discovered_channels:
                self.crawl_queue.append(channel)
                self.discovered_channels.add(channel)

        logger.info(f"添加了 {len(seed_channels)} 个种子频道")

    def discover_channels_from_url(self, channel_url: str, max_discover: int = 8) -> List[str]:
        """从频道URL发现推荐频道"""
        try:
            # 构建频道页面URL
            base_url = channel_url.rstrip('/')
            if '/videos' in base_url or '/about' in base_url:
                base_url = base_url.split('/videos')[0].split('/about')[0]

            discovered = set()

            # 尝试从channels页面发现
            channels_page = f"{base_url}/channels"
            channels_from_page = self._get_channels_from_url(channels_page)
            discovered.update(channels_from_page)

            # 从主页面发现
            if len(discovered) < max_discover:
                channels_from_main = self._get_channels_from_url(base_url)
                discovered.update(channels_from_main)

            # 过滤新频道
            new_channels = []
            for url in discovered:
                if (len(new_channels) >= max_discover):
                    break
                if (url not in self.discovered_channels and
                    url != channel_url and
                    self._is_valid_channel_url(url)):
                    new_channels.append(url)
                    self.discovered_channels.add(url)

            return new_channels

        except Exception as e:
            logger.error(f"发现频道失败: {channel_url}, 错误: {e}")
            return []

    def _get_channels_from_url(self, url: str) -> List[str]:
        """从URL获取频道链接"""
        try:
            proxy = self.proxy_pool.get_proxy()

            response = self.session.get(
                url,
                proxies=proxy,
                timeout=self.proxy_pool.config.timeout,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_pool.mark_proxy_success(proxy['id'])
                return self._extract_channel_urls(response.text)
            else:
                if proxy:
                    self.proxy_pool.mark_proxy_failed(proxy['id'])
                return []

        except Exception as e:
            if proxy:
                self.proxy_pool.mark_proxy_failed(proxy['id'])
            logger.debug(f"获取频道链接失败: {e}")
            return []

    def _extract_channel_urls(self, html: str) -> List[str]:
        """从HTML提取频道URL"""
        channels = set()

        patterns = [
            r'https://www\.youtube\.com/@([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/c/([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/channel/([a-zA-Z0-9_.-]+)',
            r'"/@([a-zA-Z0-9_.-]+)"',
            r'"/c/([a-zA-Z0-9_.-]+)"',
            r'"/channel/([a-zA-Z0-9_.-]+)"',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                if match.startswith('https://'):
                    channels.add(match)
                else:
                    if '/' in match:
                        full_url = f"https://www.youtube.com/{match}"
                    else:
                        full_url = f"https://www.youtube.com/@{match}"
                    channels.add(full_url)

        return list(channels)[:20]

    def _is_valid_channel_url(self, url: str) -> bool:
        """验证频道URL"""
        if not url or not url.startswith('https://www.youtube.com/'):
            return False

        patterns = [
            r'youtube\.com/@[a-zA-Z0-9_.-]+$',
            r'youtube\.com/c/[a-zA-Z0-9_.-]+$',
            r'youtube\.com/channel/[a-zA-Z0-9_.-]+$',
        ]

        return any(re.search(p, url, re.IGNORECASE) for p in patterns)

    def crawl_channel_for_twitter(self, channel_url: str) -> Dict:
        """爬取频道寻找Twitter链接"""
        result = {
            'channel_url': channel_url,
            'twitter_links': [],
            'success': False,
            'error': None,
            'proxy_used': None
        }

        try:
            proxy = self.proxy_pool.get_proxy()
            result['proxy_used'] = f"{proxy['username']}@{proxy['host']}" if proxy else "无代理"

            # 尝试访问频道的about页面
            about_url = f"{channel_url.rstrip('/')}/about"

            response = self.session.get(
                about_url,
                proxies=proxy,
                timeout=self.proxy_pool.config.timeout,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_pool.mark_proxy_success(proxy['id'])

                # 提取Twitter链接
                twitter_links = self.twitter_extractor.extract_from_html(response.text)
                result['twitter_links'] = twitter_links
                result['success'] = True

                if twitter_links:
                    logger.info(f"✅ 发现Twitter链接: {channel_url} -> {twitter_links}")
                    self.twitter_discoveries.append({
                        'channel_url': channel_url,
                        'twitter_links': twitter_links,
                        'discovered_at': datetime.now().isoformat(),
                        'proxy_used': result['proxy_used']
                    })
                else:
                    logger.debug(f"⚪ 未发现Twitter链接: {channel_url}")

            else:
                if proxy:
                    self.proxy_pool.mark_proxy_failed(proxy['id'])
                result['error'] = f"HTTP {response.status_code}"
                logger.warning(f"❌ 访问失败: {channel_url} (HTTP {response.status_code})")

        except Exception as e:
            if proxy:
                self.proxy_pool.mark_proxy_failed(proxy['id'])
            result['error'] = str(e)
            logger.error(f"❌ 爬取错误: {channel_url}, 错误: {e}")

        return result

    def print_twitter_discoveries(self):
        """打印发现的Twitter链接"""
        if not self.twitter_discoveries:
            print("\n🐦 暂未发现Twitter链接")
            return

        print(f"\n🐦 发现的Twitter链接 (共 {len(self.twitter_discoveries)} 个):")
        print("=" * 80)

        for i, discovery in enumerate(self.twitter_discoveries, 1):
            print(f"\n{i}. YouTube频道: {discovery['channel_url']}")
            print(f"   Twitter链接:")
            for link in discovery['twitter_links']:
                print(f"   🔗 {link}")
            print(f"   发现时间: {discovery['discovered_at']}")
            print(f"   使用代理: {discovery['proxy_used']}")
            print("-" * 60)

    def get_statistics(self) -> Dict:
        """获取爬取统计"""
        proxy_stats = self.proxy_pool.get_stats()

        return {
            'total_discovered': len(self.discovered_channels),
            'total_crawled': self.total_crawled,
            'queue_size': len(self.crawl_queue),
            'twitter_found': len(self.twitter_discoveries),
            'twitter_channels': len([d for d in self.twitter_discoveries if d['twitter_links']]),
            'proxy_stats': proxy_stats,
            'discovery_rate': f"{(len(self.twitter_discoveries)/max(self.total_crawled, 1))*100:.1f}%"
        }

    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()

        print(f"\n📊 爬取统计:")
        print(f"   🕷️  已发现频道: {stats['total_discovered']}")
        print(f"   ✅ 已爬取频道: {stats['total_crawled']}")
        print(f"   📋 队列中频道: {stats['queue_size']}")
        print(f"   🐦 发现Twitter: {stats['twitter_found']} 个链接")
        print(f"   📈 发现率: {stats['discovery_rate']}")
        print(f"   🌐 代理状态: {stats['proxy_stats']['active_proxies']}/{stats['proxy_stats']['total_proxies']} 可用 ({stats['proxy_stats']['success_rate']})")

    def continuous_crawl(self, max_total_crawl: int = 200, max_discover_per_channel: int = 6):
        """持续爬取模式"""
        logger.info(f"🚀 开始持续爬取模式 (目标: {max_total_crawl} 个频道)")

        try:
            while self.is_running and self.total_crawled < max_total_crawl:
                # 检查队列
                if not self.crawl_queue:
                    logger.warning("⚠️ 爬取队列为空，停止爬取")
                    break

                # 获取下一个频道
                current_channel = self.crawl_queue.pop(0)

                if current_channel in self.crawled_channels:
                    continue

                logger.info(f"🔍 正在爬取: {current_channel} ({self.total_crawled + 1}/{max_total_crawl})")

                # 爬取当前频道
                crawl_result = self.crawl_channel_for_twitter(current_channel)
                self.crawled_channels.add(current_channel)
                self.total_crawled += 1

                # 发现新频道
                if crawl_result['success']:
                    new_channels = self.discover_channels_from_url(current_channel, max_discover_per_channel)
                    if new_channels:
                        self.crawl_queue.extend(new_channels)
                        logger.info(f"🆕 发现 {len(new_channels)} 个新频道")

                # 每10个频道打印一次统计
                if self.total_crawled % 10 == 0:
                    self.print_statistics()
                    self.print_twitter_discoveries()

                # 随机延迟
                delay = random.uniform(2, 5)
                time.sleep(delay)

        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断爬取")
            self.is_running = False
        except Exception as e:
            logger.error(f"❌ 持续爬取错误: {e}")

        # 最终统计
        logger.info("🏁 爬取完成")
        self.print_statistics()
        self.print_twitter_discoveries()

    def stop(self):
        """停止爬取"""
        self.is_running = False
        logger.info("🛑 停止信号已发送")

def run_continuous_spider_demo():
    """运行持续爬取演示"""
    print("🕷️ YouTube持续蜘蛛式爬虫 v3.0")
    print("=" * 50)

    # 初始化爬虫
    spider = ContinuousSpider()

    # 添加种子频道
    seed_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie",
        "https://www.youtube.com/@Markiplier"
    ]

    spider.add_seed_channels(seed_channels)

    print(f"🌱 种子频道: {len(seed_channels)} 个")
    print("🚀 开始持续爬取...")

    try:
        # 开始持续爬取
        spider.continuous_crawl(
            max_total_crawl=100,  # 爬取100个频道
            max_discover_per_channel=8  # 每个频道发现8个新频道
        )
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
        spider.stop()

    return spider

if __name__ == "__main__":
    spider = run_continuous_spider_demo()
