#!/usr/bin/env python3
"""
YouTube-Twitter爬虫系统 (Google Colab版本)
专为Google Colab环境设计的完整爬虫系统

功能特点:
- 代理池管理和自动轮换
- 重试机制和错误恢复
- YouTube频道与Twitter账号映射
- 数据持久化和导出
- 实时进度显示
- 自动去重和数据验证

作者: GreenJoson
创建时间: 2025-06-26
适用环境: Google Colab (https://colab.research.google.com/)
"""

import requests
import sqlite3
import json
import time
import random
import re
from typing import List, Dict, Optional, Set
from urllib.parse import urlparse
from datetime import datetime
import logging
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('youtube_crawler.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """代理配置类"""
    username: str
    password: str
    endpoint: str
    port: int = 80
    max_failures: int = 3
    timeout: int = 25

@dataclass
class CrawlerConfig:
    """爬虫配置类"""
    max_workers: int = 3
    request_delay: tuple = (3, 6)
    max_retries: int = 3
    batch_size: int = 10
    enable_proxy: bool = True

class ProxyPool:
    """代理池管理器"""

    def __init__(self, proxy_config: ProxyConfig):
        self.config = proxy_config
        self.proxies = self._generate_proxy_list()
        self.failed_proxies = set()
        self.proxy_stats = {}
        self.current_index = 0
        self.lock = threading.Lock()

        logger.info(f"代理池初始化完成，共 {len(self.proxies)} 个代理")

    def _generate_proxy_list(self) -> List[Dict]:
        """生成代理列表 (1-100)"""
        proxies = []
        for i in range(1, 101):  # 1-100
            username = f"gorkpiln-au-ca-gb-us-{i}"
            proxy_url = f"http://{username}:{self.config.password}@{self.config.endpoint}:{self.config.port}"
            proxies.append({
                'http': proxy_url,
                'https': proxy_url,
                'id': i,
                'username': username
            })
        return proxies

    def get_proxy(self) -> Optional[Dict]:
        """获取可用代理"""
        with self.lock:
            available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]

            if not available_proxies:
                logger.warning("所有代理都已失败，重置失败列表")
                self.failed_proxies.clear()
                available_proxies = self.proxies

            if available_proxies:
                # 轮换选择
                proxy = available_proxies[self.current_index % len(available_proxies)]
                self.current_index += 1
                return proxy

            return None

    def mark_proxy_failed(self, proxy_id: int):
        """标记代理失败"""
        with self.lock:
            self.failed_proxies.add(proxy_id)
            if proxy_id not in self.proxy_stats:
                self.proxy_stats[proxy_id] = {'failures': 0, 'successes': 0}
            self.proxy_stats[proxy_id]['failures'] += 1

            logger.warning(f"代理 {proxy_id} 标记为失败，总失败代理数: {len(self.failed_proxies)}")

    def mark_proxy_success(self, proxy_id: int):
        """标记代理成功"""
        with self.lock:
            if proxy_id not in self.proxy_stats:
                self.proxy_stats[proxy_id] = {'failures': 0, 'successes': 0}
            self.proxy_stats[proxy_id]['successes'] += 1

    def get_stats(self) -> Dict:
        """获取代理统计"""
        with self.lock:
            total_proxies = len(self.proxies)
            failed_proxies = len(self.failed_proxies)
            active_proxies = total_proxies - failed_proxies

            return {
                'total_proxies': total_proxies,
                'active_proxies': active_proxies,
                'failed_proxies': failed_proxies,
                'success_rate': f"{(active_proxies/total_proxies)*100:.1f}%",
                'proxy_stats': dict(self.proxy_stats)
            }

class TwitterExtractor:
    """Twitter链接提取器"""

    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]

    def extract_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()

        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 标准化链接
                if not match.startswith('http'):
                    match = f"https://{match}"

                # 清理链接
                clean_link = match.split('?')[0].split('#')[0]

                # 转换x.com为twitter.com
                clean_link = clean_link.replace('x.com', 'twitter.com')

                twitter_links.add(clean_link)

        return sorted(list(twitter_links))

    def extract_username(self, twitter_url: str) -> str:
        """提取Twitter用户名"""
        match = re.search(r'(?:twitter\.com|x\.com)/([a-zA-Z0-9_]+)', twitter_url, re.IGNORECASE)
        if match:
            return f"@{match.group(1)}"
        return ""

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = "youtube_twitter_mappings.db"):
        self.db_path = db_path
        self.init_database()
        logger.info(f"数据库初始化完成: {db_path}")

    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # YouTube频道表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS youtube_channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id TEXT UNIQUE NOT NULL,
                    channel_url TEXT NOT NULL,
                    channel_name TEXT,
                    subscriber_count INTEGER,
                    video_count INTEGER,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active'
                )
            ''')

            # Twitter映射表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS twitter_mappings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id TEXT NOT NULL,
                    youtube_url TEXT NOT NULL,
                    twitter_url TEXT NOT NULL,
                    twitter_username TEXT,
                    discovery_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    FOREIGN KEY (channel_id) REFERENCES youtube_channels (channel_id),
                    UNIQUE(channel_id, twitter_url)
                )
            ''')

            # 爬取日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_url TEXT,
                    action TEXT,
                    status TEXT,
                    message TEXT,
                    proxy_id INTEGER,
                    response_time REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_channel_id ON youtube_channels(channel_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_twitter_url ON twitter_mappings(twitter_url)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_crawl_date ON crawl_logs(created_at)')

    def save_channel(self, channel_data: Dict) -> bool:
        """保存频道数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO youtube_channels
                    (channel_id, channel_url, channel_name, subscriber_count, video_count, description, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    channel_data.get('channel_id'),
                    channel_data.get('channel_url'),
                    channel_data.get('channel_name'),
                    channel_data.get('subscriber_count'),
                    channel_data.get('video_count'),
                    channel_data.get('description'),
                    datetime.now()
                ))

                return True
        except Exception as e:
            logger.error(f"保存频道数据失败: {e}")
            return False

    def save_twitter_mapping(self, channel_id: str, youtube_url: str, twitter_url: str, twitter_username: str) -> bool:
        """保存Twitter映射"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR IGNORE INTO twitter_mappings
                    (channel_id, youtube_url, twitter_url, twitter_username)
                    VALUES (?, ?, ?, ?)
                ''', (channel_id, youtube_url, twitter_url, twitter_username))

                return True
        except Exception as e:
            logger.error(f"保存Twitter映射失败: {e}")
            return False

    def log_crawl(self, channel_url: str, action: str, status: str, message: str, proxy_id: int = None, response_time: float = None):
        """记录爬取日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO crawl_logs
                    (channel_url, action, status, message, proxy_id, response_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (channel_url, action, status, message, proxy_id, response_time))

        except Exception as e:
            logger.error(f"记录日志失败: {e}")

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总频道数
                cursor.execute('SELECT COUNT(*) FROM youtube_channels')
                total_channels = cursor.fetchone()[0]

                # 有Twitter的频道数
                cursor.execute('''
                    SELECT COUNT(DISTINCT channel_id) FROM twitter_mappings
                    WHERE status = 'active'
                ''')
                twitter_channels = cursor.fetchone()[0]

                # 总Twitter链接数
                cursor.execute('SELECT COUNT(*) FROM twitter_mappings WHERE status = "active"')
                total_twitter_links = cursor.fetchone()[0]

                # 最近爬取时间
                cursor.execute('SELECT MAX(created_at) FROM crawl_logs')
                last_crawl = cursor.fetchone()[0]

                return {
                    'total_channels': total_channels,
                    'twitter_channels': twitter_channels,
                    'total_twitter_links': total_twitter_links,
                    'twitter_discovery_rate': f"{(twitter_channels/total_channels)*100:.1f}%" if total_channels > 0 else "0%",
                    'last_crawl': last_crawl
                }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def export_mappings(self, format: str = 'json') -> str:
        """导出映射数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT yc.channel_name, yc.channel_url, yc.subscriber_count,
                           tm.twitter_url, tm.twitter_username, tm.discovery_date
                    FROM youtube_channels yc
                    JOIN twitter_mappings tm ON yc.channel_id = tm.channel_id
                    WHERE tm.status = 'active'
                    ORDER BY yc.subscriber_count DESC, tm.discovery_date DESC
                ''')

                results = cursor.fetchall()

                if format == 'json':
                    data = []
                    for row in results:
                        data.append({
                            'channel_name': row[0],
                            'youtube_url': row[1],
                            'subscriber_count': row[2],
                            'twitter_url': row[3],
                            'twitter_username': row[4],
                            'discovery_date': row[5]
                        })

                    filename = f"youtube_twitter_mappings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                    return filename

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return ""

class YouTubeCrawler:
    """YouTube爬虫核心类"""

    def __init__(self, proxy_pool: ProxyPool, db_manager: DatabaseManager, config: CrawlerConfig):
        self.proxy_pool = proxy_pool
        self.db = db_manager
        self.config = config
        self.twitter_extractor = TwitterExtractor()
        self.session = requests.Session()

        # 已发现的频道集合，用于去重
        self.discovered_channels = set()

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        logger.info("YouTube爬虫初始化完成")

    def extract_channel_id(self, channel_url: str) -> str:
        """从URL提取频道ID"""
        # 处理不同格式的YouTube URL
        patterns = [
            r'youtube\.com/@([^/?]+)',
            r'youtube\.com/c/([^/?]+)',
            r'youtube\.com/channel/([^/?]+)',
            r'youtube\.com/user/([^/?]+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, channel_url, re.IGNORECASE)
            if match:
                return match.group(1)

        return channel_url.split('/')[-1] if '/' in channel_url else channel_url

    def crawl_channel_with_retry(self, channel_url: str) -> Optional[Dict]:
        """带重试机制的频道爬取"""
        for attempt in range(self.config.max_retries):
            try:
                result = self._crawl_channel_single(channel_url, attempt + 1)
                if result:
                    return result

            except Exception as e:
                logger.warning(f"爬取尝试 {attempt + 1} 失败: {e}")

                if attempt < self.config.max_retries - 1:
                    delay = random.uniform(2, 5) * (attempt + 1)
                    logger.info(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)

        logger.error(f"频道爬取最终失败: {channel_url}")
        return None

    def _crawl_channel_single(self, channel_url: str, attempt: int) -> Optional[Dict]:
        """单次频道爬取"""
        start_time = time.time()
        proxy = None
        proxy_id = None

        try:
            # 获取代理
            if self.config.enable_proxy:
                proxy = self.proxy_pool.get_proxy()
                if not proxy:
                    raise Exception("无可用代理")
                proxy_id = proxy['id']

            # 发送请求
            response = self.session.get(
                channel_url,
                proxies=proxy if proxy else None,
                timeout=25,
                allow_redirects=True
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                # 解析频道信息
                channel_data = self._parse_channel_page(response.text, channel_url)

                if channel_data:
                    # 标记代理成功
                    if proxy_id:
                        self.proxy_pool.mark_proxy_success(proxy_id)

                    # 记录日志
                    self.db.log_crawl(
                        channel_url, 'crawl', 'success',
                        f"成功爬取频道: {channel_data.get('channel_name', 'Unknown')}",
                        proxy_id, response_time
                    )

                    logger.info(f"✅ 频道爬取成功: {channel_data.get('channel_name', 'Unknown')} (用时: {response_time:.2f}s)")
                    return channel_data
                else:
                    raise Exception("页面解析失败")
            else:
                raise Exception(f"HTTP {response.status_code}")

        except Exception as e:
            response_time = time.time() - start_time

            # 标记代理失败
            if proxy_id:
                self.proxy_pool.mark_proxy_failed(proxy_id)

            # 记录日志
            self.db.log_crawl(
                channel_url, 'crawl', 'failed',
                f"尝试 {attempt}: {str(e)}",
                proxy_id, response_time
            )

            logger.warning(f"❌ 频道爬取失败 (尝试 {attempt}): {e}")
            raise

    def _parse_channel_page(self, html_content: str, channel_url: str) -> Optional[Dict]:
        """解析频道页面"""
        try:
            # 提取频道名称
            channel_name = self._extract_channel_name(html_content)

            # 提取订阅者数量
            subscriber_count = self._extract_subscriber_count(html_content)

            # 提取视频数量
            video_count = self._extract_video_count(html_content)

            # 提取描述
            description = self._extract_description(html_content)

            # 提取Twitter链接
            twitter_links = self.twitter_extractor.extract_from_html(html_content)

            # 生成频道ID
            channel_id = self.extract_channel_id(channel_url)

            channel_data = {
                'channel_id': channel_id,
                'channel_url': channel_url,
                'channel_name': channel_name,
                'subscriber_count': subscriber_count,
                'video_count': video_count,
                'description': description,
                'twitter_links': twitter_links
            }

            # 保存到数据库
            self.db.save_channel(channel_data)

            # 保存Twitter映射
            for twitter_url in twitter_links:
                twitter_username = self.twitter_extractor.extract_username(twitter_url)
                self.db.save_twitter_mapping(channel_id, channel_url, twitter_url, twitter_username)

            return channel_data

        except Exception as e:
            logger.error(f"解析频道页面失败: {e}")
            return None

    def _extract_channel_name(self, html: str) -> str:
        """提取频道名称"""
        patterns = [
            r'"channelMetadataRenderer":\s*{[^}]*"title":\s*"([^"]+)"',
            r'<meta\s+property="og:title"\s+content="([^"]+)"',
            r'"title":\s*"([^"]+)"\s*,\s*"navigationEndpoint"',
            r'<title>([^<]+)</title>'
        ]

        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                if name and name != "YouTube":
                    return name

        return "Unknown Channel"

    def _extract_subscriber_count(self, html: str) -> int:
        """提取订阅者数量"""
        patterns = [
            r'"subscriberCountText":\s*{[^}]*"simpleText":\s*"([^"]+)"',
            r'"subscriberCountText":\s*{[^}]*"runs":\s*\[[^]]*"text":\s*"([^"]+)"',
        ]

        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                count_text = match.group(1)
                return self._parse_count(count_text)

        return 0

    def _extract_video_count(self, html: str) -> int:
        """提取视频数量"""
        patterns = [
            r'"videosCountText":\s*{[^}]*"runs":\s*\[[^]]*"text":\s*"([^"]+)"',
            r'(\d+(?:,\d+)*)\s*videos?',
        ]

        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                count_text = match.group(1)
                return self._parse_count(count_text)

        return 0

    def _extract_description(self, html: str) -> str:
        """提取频道描述"""
        patterns = [
            r'"description":\s*{[^}]*"simpleText":\s*"([^"]+)"',
            r'<meta\s+property="og:description"\s+content="([^"]+)"',
        ]

        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return ""

    def _parse_count(self, count_text: str) -> int:
        """解析数量文本"""
        if not count_text:
            return 0

        # 移除非数字字符，保留数字、逗号、点、K、M、B
        clean_text = re.sub(r'[^\d.,KMB]', '', count_text.upper())

        if not clean_text:
            return 0

        try:
            # 处理K、M、B后缀
            multiplier = 1
            if 'K' in clean_text:
                multiplier = 1000
                clean_text = clean_text.replace('K', '')
            elif 'M' in clean_text:
                multiplier = 1000000
                clean_text = clean_text.replace('M', '')
            elif 'B' in clean_text:
                multiplier = 1000000000
                clean_text = clean_text.replace('B', '')

            # 移除逗号
            clean_text = clean_text.replace(',', '')

            # 转换为数字
            if '.' in clean_text:
                number = float(clean_text)
            else:
                number = int(clean_text)

            return int(number * multiplier)

        except (ValueError, TypeError):
            return 0

    def discover_recommended_channels(self, seed_channel_url: str, max_channels: int = 10) -> List[str]:
        """
        从种子频道发现推荐频道

        Args:
            seed_channel_url: 种子频道URL
            max_channels: 最大发现频道数

        Returns:
            List[str]: 发现的频道URL列表
        """
        try:
            logger.info(f"🔍 从种子频道发现推荐频道: {seed_channel_url}")

            # 构建频道页面URL（确保访问频道主页）
            if '/videos' in seed_channel_url or '/about' in seed_channel_url:
                base_url = seed_channel_url.split('/videos')[0].split('/about')[0]
            else:
                base_url = seed_channel_url.rstrip('/')

            # 尝试访问频道的channels页面（推荐频道页面）
            channels_page_url = f"{base_url}/channels"

            discovered_urls = set()

            # 方法1: 尝试从channels页面发现
            channels_from_page = self._discover_from_channels_page(channels_page_url)
            discovered_urls.update(channels_from_page)

            # 方法2: 从主页面发现推荐频道
            if len(discovered_urls) < max_channels:
                channels_from_main = self._discover_from_main_page(base_url)
                discovered_urls.update(channels_from_main)

            # 去重并限制数量
            result = []
            for url in discovered_urls:
                if len(result) >= max_channels:
                    break
                if url not in self.discovered_channels and url != seed_channel_url:
                    result.append(url)
                    self.discovered_channels.add(url)

            logger.info(f"✅ 从 {seed_channel_url} 发现了 {len(result)} 个新频道")
            return result

        except Exception as e:
            logger.error(f"发现推荐频道失败: {seed_channel_url}, 错误: {e}")
            return []

    def _discover_from_channels_page(self, channels_page_url: str) -> List[str]:
        """从频道的channels页面发现推荐频道"""
        try:
            proxy = self.proxy_pool.get_proxy() if self.config.enable_proxy else None

            response = self.session.get(
                channels_page_url,
                proxies=proxy,
                timeout=25,
                allow_redirects=True
            )

            if response.status_code == 200:
                return self._extract_channel_urls_from_html(response.text)
            else:
                logger.debug(f"Channels页面访问失败: {response.status_code}")
                return []

        except Exception as e:
            logger.debug(f"访问channels页面异常: {e}")
            return []

    def _discover_from_main_page(self, main_page_url: str) -> List[str]:
        """从频道主页发现推荐频道"""
        try:
            proxy = self.proxy_pool.get_proxy() if self.config.enable_proxy else None

            response = self.session.get(
                main_page_url,
                proxies=proxy,
                timeout=25,
                allow_redirects=True
            )

            if response.status_code == 200:
                return self._extract_channel_urls_from_html(response.text)
            else:
                logger.debug(f"主页面访问失败: {response.status_code}")
                return []

        except Exception as e:
            logger.debug(f"访问主页面异常: {e}")
            return []

    def _extract_channel_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取频道URL"""
        channel_urls = set()

        # YouTube频道URL的正则表达式模式
        patterns = [
            r'https://www\.youtube\.com/@([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/c/([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/channel/([a-zA-Z0-9_.-]+)',
            r'https://www\.youtube\.com/user/([a-zA-Z0-9_.-]+)',
            r'/(@[a-zA-Z0-9_.-]+)',
            r'/(c/[a-zA-Z0-9_.-]+)',
            r'/(channel/[a-zA-Z0-9_.-]+)',
            r'/(user/[a-zA-Z0-9_.-]+)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match.startswith('https://'):
                    channel_urls.add(match)
                else:
                    # 构建完整URL
                    if match.startswith('@'):
                        full_url = f"https://www.youtube.com/{match}"
                    elif match.startswith(('c/', 'channel/', 'user/')):
                        full_url = f"https://www.youtube.com/{match}"
                    else:
                        continue

                    channel_urls.add(full_url)

        # 过滤掉无效的URL
        valid_urls = []
        for url in channel_urls:
            if self._is_valid_youtube_channel_url(url):
                valid_urls.append(url)

        return valid_urls[:20]  # 限制返回数量

    def _is_valid_youtube_channel_url(self, url: str) -> bool:
        """验证YouTube频道URL是否有效"""
        if not url or not isinstance(url, str):
            return False

        # 基本格式检查
        if not url.startswith('https://www.youtube.com/'):
            return False

        # 检查是否包含有效的频道标识符
        valid_patterns = [
            r'youtube\.com/@[a-zA-Z0-9_.-]+$',
            r'youtube\.com/c/[a-zA-Z0-9_.-]+$',
            r'youtube\.com/channel/[a-zA-Z0-9_.-]+$',
            r'youtube\.com/user/[a-zA-Z0-9_.-]+$',
        ]

        for pattern in valid_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return True

        return False

class CrawlerController:
    """爬虫控制器 - 专为Google Colab设计"""

    def __init__(self, webshare_username_base: str = "gorkpiln-au-ca-gb-us",
                 webshare_password: str = "6lwffvf16jjn",
                 webshare_endpoint: str = "p.webshare.io"):

        # 初始化配置
        self.proxy_config = ProxyConfig(
            username=webshare_username_base,
            password=webshare_password,
            endpoint=webshare_endpoint
        )

        self.crawler_config = CrawlerConfig(
            max_workers=3,
            request_delay=(3, 6),
            max_retries=3,
            batch_size=10,
            enable_proxy=True
        )

        # 初始化组件
        self.proxy_pool = ProxyPool(self.proxy_config)
        self.db = DatabaseManager()
        self.crawler = YouTubeCrawler(self.proxy_pool, self.db, self.crawler_config)

        # 预定义频道列表
        self.popular_channels = [
            "https://www.youtube.com/@MrBeast",
            "https://www.youtube.com/@PewDiePie",
            "https://www.youtube.com/@Markiplier",
            "https://www.youtube.com/@LinusTechTips",
            "https://www.youtube.com/@MKBHD",
            "https://www.youtube.com/@UnboxTherapy",
            "https://www.youtube.com/@veritasium",
            "https://www.youtube.com/@3Blue1Brown",
            "https://www.youtube.com/@TED",
            "https://www.youtube.com/@NatGeo",
            "https://www.youtube.com/@BBCNews",
            "https://www.youtube.com/@CNN",
            "https://www.youtube.com/@TheEllenShow",
            "https://www.youtube.com/@JimmyKimmelLive",
            "https://www.youtube.com/@TeamCoco",
            "https://www.youtube.com/@SethMyers",
            "https://www.youtube.com/@StephenCurryFamily",
            "https://www.youtube.com/@NBA",
            "https://www.youtube.com/@SpaceX",
            "https://www.youtube.com/@Tesla"
        ]

        logger.info("🚀 YouTube-Twitter爬虫控制器初始化完成")
        logger.info(f"📊 代理池: {len(self.proxy_pool.proxies)} 个代理")
        logger.info(f"🎯 预设频道: {len(self.popular_channels)} 个")

    def test_proxy_connection(self, test_count: int = 5) -> Dict:
        """测试代理连接"""
        print("🔍 测试代理连接...")

        test_results = []
        test_url = "https://httpbin.org/ip"

        for i in range(min(test_count, len(self.proxy_pool.proxies))):
            proxy = self.proxy_pool.get_proxy()
            if not proxy:
                break

            try:
                start_time = time.time()
                response = requests.get(test_url, proxies=proxy, timeout=15)
                response_time = time.time() - start_time

                if response.status_code == 200:
                    ip_info = response.json()
                    test_results.append({
                        'proxy_id': proxy['id'],
                        'status': 'success',
                        'ip': ip_info.get('origin', 'Unknown'),
                        'response_time': response_time
                    })
                    self.proxy_pool.mark_proxy_success(proxy['id'])
                    print(f"  ✅ 代理 {proxy['id']}: {ip_info.get('origin')} ({response_time:.2f}s)")
                else:
                    test_results.append({
                        'proxy_id': proxy['id'],
                        'status': 'failed',
                        'error': f"HTTP {response.status_code}"
                    })
                    self.proxy_pool.mark_proxy_failed(proxy['id'])
                    print(f"  ❌ 代理 {proxy['id']}: HTTP {response.status_code}")

            except Exception as e:
                test_results.append({
                    'proxy_id': proxy['id'],
                    'status': 'failed',
                    'error': str(e)
                })
                self.proxy_pool.mark_proxy_failed(proxy['id'])
                print(f"  ❌ 代理 {proxy['id']}: {e}")

            time.sleep(1)  # 避免请求过快

        success_count = sum(1 for r in test_results if r['status'] == 'success')
        print(f"\n📊 代理测试结果: {success_count}/{len(test_results)} 成功")

        return {
            'total_tested': len(test_results),
            'successful': success_count,
            'success_rate': f"{(success_count/len(test_results))*100:.1f}%" if test_results else "0%",
            'results': test_results
        }

    def crawl_batch(self, channel_urls: List[str], show_progress: bool = True) -> Dict:
        """批量爬取频道"""
        print(f"🎯 开始批量爬取 {len(channel_urls)} 个频道...")

        results = {
            'successful': [],
            'failed': [],
            'twitter_discoveries': [],
            'total_twitter_links': 0
        }

        for i, channel_url in enumerate(channel_urls, 1):
            if show_progress:
                print(f"\n[{i}/{len(channel_urls)}] 🔍 爬取: {channel_url}")

            try:
                channel_data = self.crawler.crawl_channel_with_retry(channel_url)

                if channel_data:
                    results['successful'].append(channel_data)

                    twitter_links = channel_data.get('twitter_links', [])
                    if twitter_links:
                        results['twitter_discoveries'].append({
                            'channel_name': channel_data.get('channel_name'),
                            'channel_url': channel_url,
                            'twitter_links': twitter_links
                        })
                        results['total_twitter_links'] += len(twitter_links)

                        if show_progress:
                            print(f"  🐦 发现 {len(twitter_links)} 个Twitter链接:")
                            for link in twitter_links:
                                print(f"    - {link}")
                    else:
                        if show_progress:
                            print(f"  ⚠️  未发现Twitter链接")
                else:
                    results['failed'].append(channel_url)
                    if show_progress:
                        print(f"  ❌ 爬取失败")

            except Exception as e:
                results['failed'].append(channel_url)
                if show_progress:
                    print(f"  ❌ 爬取异常: {e}")

            # 请求间隔
            if i < len(channel_urls):
                delay = random.uniform(*self.crawler_config.request_delay)
                if show_progress:
                    print(f"  ⏳ 等待 {delay:.1f} 秒...")
                time.sleep(delay)

        return results

    def discover_and_crawl(self, seed_channels: List[str], max_discover_per_seed: int = 5,
                          max_total_channels: int = 50, show_progress: bool = True) -> Dict:
        """
        蜘蛛式发现和爬取频道

        Args:
            seed_channels: 种子频道列表
            max_discover_per_seed: 每个种子频道最大发现数
            max_total_channels: 总最大频道数
            show_progress: 是否显示进度

        Returns:
            Dict: 发现和爬取结果
        """
        print(f"🕷️ 开始蜘蛛式频道发现和爬取...")
        print(f"🌱 种子频道: {len(seed_channels)} 个")
        print(f"🎯 每个种子最大发现: {max_discover_per_seed} 个")
        print(f"📊 总最大频道数: {max_total_channels} 个")

        all_discovered_channels = set()
        crawl_queue = list(seed_channels)  # 爬取队列
        crawled_channels = set()  # 已爬取频道

        results = {
            'seed_channels': seed_channels,
            'discovered_channels': [],
            'successful_crawls': [],
            'failed_crawls': [],
            'twitter_discoveries': [],
            'total_twitter_links': 0,
            'discovery_stats': {}
        }

        round_num = 1

        while crawl_queue and len(results['successful_crawls']) < max_total_channels:
            print(f"\n🔄 第 {round_num} 轮发现和爬取")
            print(f"📋 待爬取队列: {len(crawl_queue)} 个频道")

            current_batch = crawl_queue[:min(10, len(crawl_queue))]  # 每批最多10个
            crawl_queue = crawl_queue[len(current_batch):]

            # 爬取当前批次
            for i, channel_url in enumerate(current_batch, 1):
                if len(results['successful_crawls']) >= max_total_channels:
                    break

                if channel_url in crawled_channels:
                    continue

                if show_progress:
                    print(f"\n[{round_num}.{i}] 🔍 爬取: {channel_url}")

                try:
                    # 爬取频道
                    channel_data = self.crawler.crawl_channel_with_retry(channel_url)
                    crawled_channels.add(channel_url)

                    if channel_data:
                        results['successful_crawls'].append(channel_data)

                        # 检查Twitter发现
                        twitter_links = channel_data.get('twitter_links', [])
                        if twitter_links:
                            results['twitter_discoveries'].append({
                                'channel_name': channel_data.get('channel_name'),
                                'channel_url': channel_url,
                                'twitter_links': twitter_links
                            })
                            results['total_twitter_links'] += len(twitter_links)

                            if show_progress:
                                print(f"  🐦 发现 {len(twitter_links)} 个Twitter链接")

                        # 从成功爬取的频道发现新频道
                        if len(results['successful_crawls']) < max_total_channels:
                            discovered = self.crawler.discover_recommended_channels(
                                channel_url, max_discover_per_seed
                            )

                            if discovered:
                                new_channels = []
                                for new_url in discovered:
                                    if (new_url not in all_discovered_channels and
                                        new_url not in crawled_channels and
                                        new_url not in crawl_queue):
                                        new_channels.append(new_url)
                                        all_discovered_channels.add(new_url)
                                        crawl_queue.append(new_url)

                                if new_channels:
                                    results['discovered_channels'].extend(new_channels)
                                    results['discovery_stats'][channel_url] = len(new_channels)

                                    if show_progress:
                                        print(f"  🔍 发现 {len(new_channels)} 个新频道")
                    else:
                        results['failed_crawls'].append(channel_url)
                        if show_progress:
                            print(f"  ❌ 爬取失败")

                except Exception as e:
                    results['failed_crawls'].append(channel_url)
                    crawled_channels.add(channel_url)
                    if show_progress:
                        print(f"  ❌ 爬取异常: {e}")

                # 请求间隔
                if i < len(current_batch):
                    delay = random.uniform(*self.crawler_config.request_delay)
                    if show_progress:
                        print(f"  ⏳ 等待 {delay:.1f} 秒...")
                    time.sleep(delay)

            round_num += 1

            # 避免无限循环
            if round_num > 10:
                print("⚠️ 达到最大轮次限制，停止发现")
                break

        # 统计结果
        print(f"\n📊 蜘蛛式爬取完成:")
        print(f"  🌱 种子频道: {len(seed_channels)}")
        print(f"  🔍 发现频道: {len(results['discovered_channels'])}")
        print(f"  ✅ 成功爬取: {len(results['successful_crawls'])}")
        print(f"  ❌ 失败爬取: {len(results['failed_crawls'])}")
        print(f"  🐦 Twitter发现: {len(results['twitter_discoveries'])}")
        print(f"  🔗 总Twitter链接: {results['total_twitter_links']}")

        return results

    def quick_demo(self, channel_count: int = 5) -> Dict:
        """快速演示 - 适合Colab环境"""
        print("🚀 YouTube-Twitter爬虫快速演示")
        print("=" * 50)

        # 1. 测试代理
        print("\n1️⃣ 测试代理连接...")
        proxy_test = self.test_proxy_connection(3)

        if proxy_test['successful'] == 0:
            print("❌ 代理测试失败，无法继续")
            return {'status': 'failed', 'reason': 'proxy_failed'}

        # 2. 爬取频道
        print(f"\n2️⃣ 爬取前 {channel_count} 个热门频道...")
        demo_channels = self.popular_channels[:channel_count]
        crawl_results = self.crawl_batch(demo_channels)

        # 3. 显示结果
        print(f"\n3️⃣ 爬取结果统计:")
        print(f"  📺 总频道数: {len(demo_channels)}")
        print(f"  ✅ 成功爬取: {len(crawl_results['successful'])}")
        print(f"  ❌ 失败数量: {len(crawl_results['failed'])}")
        print(f"  🐦 发现Twitter: {len(crawl_results['twitter_discoveries'])}")
        print(f"  🔗 总Twitter链接: {crawl_results['total_twitter_links']}")

        # 4. 显示Twitter映射
        if crawl_results['twitter_discoveries']:
            print(f"\n4️⃣ 发现的Twitter映射:")
            for discovery in crawl_results['twitter_discoveries']:
                print(f"  📺 {discovery['channel_name']}")
                print(f"     YouTube: {discovery['channel_url']}")
                for twitter_link in discovery['twitter_links']:
                    print(f"     Twitter: {twitter_link}")
                print()

        # 5. 数据库统计
        print(f"\n5️⃣ 数据库统计:")
        stats = self.db.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")

        return {
            'status': 'success',
            'proxy_test': proxy_test,
            'crawl_results': crawl_results,
            'database_stats': stats
        }

    def export_results(self) -> str:
        """导出结果"""
        print("📤 导出爬取结果...")
        filename = self.db.export_mappings('json')
        if filename:
            print(f"✅ 结果已导出到: {filename}")
            return filename
        else:
            print("❌ 导出失败")
            return ""

    def get_status_report(self) -> Dict:
        """获取状态报告"""
        proxy_stats = self.proxy_pool.get_stats()
        db_stats = self.db.get_statistics()

        return {
            'proxy_pool': proxy_stats,
            'database': db_stats,
            'timestamp': datetime.now().isoformat()
        }

# Colab专用函数
def setup_colab_environment():
    """设置Colab环境"""
    print("🔧 设置Google Colab环境...")

    # 安装依赖
    import subprocess
    import sys

    required_packages = ['requests', 'loguru']

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

    print("✅ Colab环境设置完成")

def create_crawler_instance(webshare_password: str = "6lwffvf16jjn") -> CrawlerController:
    """创建爬虫实例 - Colab入口函数"""
    return CrawlerController(webshare_password=webshare_password)

def run_quick_demo(crawler: CrawlerController = None, channel_count: int = 5) -> Dict:
    """运行快速演示 - Colab入口函数"""
    if crawler is None:
        crawler = create_crawler_instance()

    return crawler.quick_demo(channel_count)

# 主函数
def main():
    """主函数 - 可在Colab中直接运行"""
    print("🎯 YouTube-Twitter爬虫系统 (Google Colab版)")
    print("专为Google Colab环境优化")
    print("=" * 60)

    try:
        # 设置环境
        setup_colab_environment()

        # 创建爬虫
        crawler = create_crawler_instance()

        # 运行演示
        results = run_quick_demo(crawler, channel_count=8)

        if results['status'] == 'success':
            print("\n🎉 演示完成！")
            print("\n💡 下一步操作:")
            print("1. crawler.crawl_batch(your_channel_list) - 爬取自定义频道")
            print("2. crawler.export_results() - 导出结果")
            print("3. crawler.get_status_report() - 查看状态")

            return crawler
        else:
            print(f"\n❌ 演示失败: {results.get('reason', 'Unknown')}")
            return None

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        return None

def run_spider_discovery_demo(crawler: CrawlerController = None, seed_count: int = 3,
                             max_discover_per_seed: int = 5, max_total: int = 50) -> Dict:
    """
    运行蜘蛛式频道发现演示 - Colab入口函数

    Args:
        crawler: 爬虫控制器实例
        seed_count: 种子频道数量
        max_discover_per_seed: 每个种子最大发现数
        max_total: 总最大频道数

    Returns:
        Dict: 发现结果
    """
    if crawler is None:
        crawler = create_crawler_instance()

    print("🕷️ YouTube频道蜘蛛式发现演示")
    print("=" * 50)

    # 使用前几个热门频道作为种子
    seed_channels = crawler.popular_channels[:seed_count]

    print(f"🌱 种子频道 ({len(seed_channels)} 个):")
    for i, channel in enumerate(seed_channels, 1):
        print(f"  {i}. {channel}")

    # 运行蜘蛛式发现
    results = crawler.discover_and_crawl(
        seed_channels=seed_channels,
        max_discover_per_seed=max_discover_per_seed,
        max_total_channels=max_total,
        show_progress=True
    )

    return results

if __name__ == '__main__':
    # 在Colab中运行: python youtube_twitter_crawler_colab.py
    # 或者导入后使用: from youtube_twitter_crawler_colab import main; crawler = main()

    print("🎯 选择运行模式:")
    print("1. 固定频道演示 (原版)")
    print("2. 蜘蛛式发现演示 (新功能)")

    try:
        choice = input("请选择 (1 或 2): ").strip()

        if choice == "2":
            print("\n🕷️ 启动蜘蛛式发现演示...")
            crawler = create_crawler_instance()
            results = run_spider_discovery_demo(crawler, seed_count=3, max_discover_per_seed=5, max_total=15)
            print("\n🎉 蜘蛛式发现演示完成！")
        else:
            print("\n🎯 启动固定频道演示...")
            crawler = main()

    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        crawler = main()  # 默认运行原版演示
